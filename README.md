# WHMCS 8.X 企业级支付网关

## 项目简介

这是一个为 WHMCS 8.X 开发的企业级支付网关模块，支持安全的在线支付处理。

## 功能特性

- ✅ 符合 WHMCS 8.X 标准的支付网关接口
- ✅ 安全的 MD5 签名算法实现
- ✅ 支持 HTTPS 传输协议
- ✅ 完整的错误处理和日志记录
- ✅ 灵活的配置管理
- ✅ 支持美元交易（单位：分）
- ✅ 支持 POST/GET 请求方式
- ✅ UTF-8 字符编码

## 目录结构

```
/
├── modules/
│   └── gateways/
│       ├── enterprise_gateway.php      # 主支付网关模块
│       └── enterprise_gateway/         # 模块相关文件
│           ├── lib/
│           │   ├── SignatureHelper.php # 签名算法核心类
│           │   ├── PaymentProcessor.php # 支付处理器
│           │   └── Logger.php          # 日志处理类
│           ├── templates/
│           │   ├── payment_form.tpl    # 支付表单模板
│           │   └── admin_config.tpl    # 管理配置模板
│           └── lang/
│               ├── english.php         # 英文语言包
│               └── chinese.php         # 中文语言包
├── docs/                               # 文档目录
│   ├── installation.md                # 安装说明
│   ├── configuration.md               # 配置说明
│   └── api_reference.md               # API 参考
├── tests/                              # 测试目录
│   ├── SignatureTest.php              # 签名算法测试
│   └── PaymentTest.php                # 支付流程测试
└── README.md                          # 项目说明
```

## 签名算法规范

### 协议规则
- **传输方式**: HTTPS
- **提交方式**: POST/GET
- **字符编码**: UTF-8
- **签名算法**: MD5

### 参数规范
- **交易金额**: 美元交易，单位为分，不能带小数
- **请求格式**: Form表单方式 (content-type: application/x-www-form-urlencoded)

### 签名生成步骤
1. 将所有非空参数按参数名ASCII码从小到大排序
2. 使用URL键值对格式拼接成字符串
3. 在字符串末尾拼接 "&key=${key}"
4. 对结果进行MD5运算并转换为大写

## 快速开始

1. 将模块文件复制到 WHMCS 安装目录
2. 在 WHMCS 管理后台启用支付网关
3. 配置商户信息和密钥
4. 测试支付流程

## 技术支持

如有问题，请查看文档或联系技术支持。

## 许可证

本项目采用企业级许可证，请遵守相关使用条款。
