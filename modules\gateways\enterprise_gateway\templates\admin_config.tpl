{*
 * WHMCS 企业级支付网关管理配置模板
 * 
 * 提供管理员配置界面，包括网关设置、状态监控、日志查看等功能
 *}

<div class="enterprise-gateway-admin">
    <div class="row">
        <div class="col-md-8">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <i class="fa fa-cog"></i> Gateway Configuration
                    </h3>
                </div>
                <div class="panel-body">
                    <form id="gateway-config-form">
                        <div class="form-group">
                            <label for="merchant_id">Merchant ID</label>
                            <input type="text" class="form-control" id="merchant_id" name="merchant_id" 
                                   value="{$config.merchant_id}" placeholder="Enter your merchant ID">
                            <small class="help-block">Your unique merchant identifier provided by the payment gateway</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="merchant_key">Merchant Key</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="merchant_key" name="merchant_key" 
                                       value="{$config.merchant_key}" placeholder="Enter your merchant key">
                                <span class="input-group-btn">
                                    <button type="button" class="btn btn-default" onclick="togglePassword('merchant_key')">
                                        <i class="fa fa-eye"></i>
                                    </button>
                                </span>
                            </div>
                            <small class="help-block">Secret key used for signature generation and verification</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="payment_url">Payment URL</label>
                            <input type="url" class="form-control" id="payment_url" name="payment_url" 
                                   value="{$config.payment_url}" placeholder="https://api.payment-gateway.com/pay">
                            <small class="help-block">Payment gateway API endpoint for processing payments</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="query_url">Query URL</label>
                            <input type="url" class="form-control" id="query_url" name="query_url" 
                                   value="{$config.query_url}" placeholder="https://api.payment-gateway.com/query">
                            <small class="help-block">API endpoint for querying payment status</small>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="test_mode">Test Mode</label>
                                    <select class="form-control" id="test_mode" name="test_mode">
                                        <option value="0" {if !$config.test_mode}selected{/if}>Disabled</option>
                                        <option value="1" {if $config.test_mode}selected{/if}>Enabled</option>
                                    </select>
                                    <small class="help-block">Enable for development and testing</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="log_level">Log Level</label>
                                    <select class="form-control" id="log_level" name="log_level">
                                        <option value="DEBUG" {if $config.log_level == 'DEBUG'}selected{/if}>Debug</option>
                                        <option value="INFO" {if $config.log_level == 'INFO'}selected{/if}>Info</option>
                                        <option value="WARNING" {if $config.log_level == 'WARNING'}selected{/if}>Warning</option>
                                        <option value="ERROR" {if $config.log_level == 'ERROR'}selected{/if}>Error</option>
                                        <option value="CRITICAL" {if $config.log_level == 'CRITICAL'}selected{/if}>Critical</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="currency_support">Supported Currencies</label>
                            <input type="text" class="form-control" id="currency_support" name="currency_support" 
                                   value="{$config.currency_support}" placeholder="USD,EUR,GBP">
                            <small class="help-block">Comma-separated list of supported currency codes</small>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fa fa-save"></i> Save Configuration
                            </button>
                            <button type="button" class="btn btn-default" onclick="testConnection()">
                                <i class="fa fa-plug"></i> Test Connection
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="panel panel-info">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <i class="fa fa-info-circle"></i> Gateway Status
                    </h3>
                </div>
                <div class="panel-body">
                    <div id="gateway-status">
                        <div class="status-item">
                            <span class="label label-default">Configuration</span>
                            <span id="config-status" class="pull-right">
                                <i class="fa fa-spinner fa-spin"></i> Checking...
                            </span>
                        </div>
                        <div class="status-item">
                            <span class="label label-default">Connection</span>
                            <span id="connection-status" class="pull-right">
                                <i class="fa fa-spinner fa-spin"></i> Checking...
                            </span>
                        </div>
                        <div class="status-item">
                            <span class="label label-default">Signature</span>
                            <span id="signature-status" class="pull-right">
                                <i class="fa fa-spinner fa-spin"></i> Checking...
                            </span>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="gateway-stats">
                        <h5>Recent Activity</h5>
                        <div class="stat-item">
                            <span>Today's Transactions:</span>
                            <span class="pull-right badge">{$stats.today_transactions|default:0}</span>
                        </div>
                        <div class="stat-item">
                            <span>Success Rate:</span>
                            <span class="pull-right badge badge-success">{$stats.success_rate|default:0}%</span>
                        </div>
                        <div class="stat-item">
                            <span>Last Transaction:</span>
                            <span class="pull-right text-muted">{$stats.last_transaction|default:'N/A'}</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="panel panel-warning">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <i class="fa fa-exclamation-triangle"></i> Security Notes
                    </h3>
                </div>
                <div class="panel-body">
                    <ul class="list-unstyled">
                        <li><i class="fa fa-check text-success"></i> Always use HTTPS in production</li>
                        <li><i class="fa fa-check text-success"></i> Keep merchant key secure</li>
                        <li><i class="fa fa-check text-success"></i> Monitor transaction logs</li>
                        <li><i class="fa fa-check text-success"></i> Test thoroughly before going live</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <i class="fa fa-file-text"></i> Recent Logs
                    </h3>
                </div>
                <div class="panel-body">
                    <div id="recent-logs">
                        <div class="text-center">
                            <i class="fa fa-spinner fa-spin"></i> Loading logs...
                        </div>
                    </div>
                    <div class="text-center">
                        <button type="button" class="btn btn-sm btn-default" onclick="loadLogs()">
                            <i class="fa fa-refresh"></i> Refresh Logs
                        </button>
                        <button type="button" class="btn btn-sm btn-default" onclick="downloadLogs()">
                            <i class="fa fa-download"></i> Download Full Log
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.enterprise-gateway-admin .status-item {
    margin-bottom: 10px;
    padding: 5px 0;
    border-bottom: 1px solid #eee;
}

.enterprise-gateway-admin .stat-item {
    margin-bottom: 8px;
    padding: 3px 0;
}

.enterprise-gateway-admin .form-actions {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.enterprise-gateway-admin #recent-logs {
    max-height: 300px;
    overflow-y: auto;
    background: #f8f8f8;
    padding: 10px;
    border-radius: 3px;
    font-family: monospace;
    font-size: 12px;
}

.log-entry {
    margin-bottom: 5px;
    padding: 2px 5px;
    border-radius: 2px;
}

.log-entry.error { background-color: #f2dede; color: #a94442; }
.log-entry.warning { background-color: #fcf8e3; color: #8a6d3b; }
.log-entry.info { background-color: #d9edf7; color: #31708f; }
.log-entry.debug { background-color: #f5f5f5; color: #666; }
</style>

<script>
function togglePassword(fieldId) {
    var field = document.getElementById(fieldId);
    var icon = field.nextElementSibling.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.className = 'fa fa-eye-slash';
    } else {
        field.type = 'password';
        icon.className = 'fa fa-eye';
    }
}

function testConnection() {
    // Implementation for testing gateway connection
    alert('Connection test functionality would be implemented here');
}

function loadLogs() {
    // Implementation for loading recent logs
    document.getElementById('recent-logs').innerHTML = '<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Loading logs...</div>';
    
    // Simulate log loading
    setTimeout(function() {
        var sampleLogs = [
            '<div class="log-entry info">[2024-01-01 12:00:00] [INFO] Payment request created successfully | Context: {"order_id":"12345"}</div>',
            '<div class="log-entry info">[2024-01-01 11:58:30] [INFO] Received payment callback | Context: {"method":"POST","ip":"***********"}</div>',
            '<div class="log-entry warning">[2024-01-01 11:55:15] [WARNING] Payment failed | Context: {"invoice_id":"12344","reason":"insufficient_funds"}</div>'
        ];
        document.getElementById('recent-logs').innerHTML = sampleLogs.join('');
    }, 1000);
}

function downloadLogs() {
    // Implementation for downloading full log file
    alert('Log download functionality would be implemented here');
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadLogs();
    
    // Check gateway status
    setTimeout(function() {
        document.getElementById('config-status').innerHTML = '<i class="fa fa-check text-success"></i> OK';
        document.getElementById('connection-status').innerHTML = '<i class="fa fa-check text-success"></i> Connected';
        document.getElementById('signature-status').innerHTML = '<i class="fa fa-check text-success"></i> Valid';
    }, 2000);
});
</script>
