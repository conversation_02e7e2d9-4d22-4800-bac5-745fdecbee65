<?php
/**
 * 企业级支付网关签名算法核心类
 * 
 * 实现符合文档要求的MD5签名算法，包括：
 * - 参数排序（ASCII码字典序）
 * - 字符串拼接
 * - 签名生成和验证
 * 
 * <AUTHOR> Gateway Team
 * @version 1.0.0
 */

namespace EnterpriseGateway\Lib;

class SignatureHelper
{
    /**
     * 商户密钥
     * @var string
     */
    private $merchantKey;

    /**
     * 构造函数
     * 
     * @param string $merchantKey 商户密钥
     */
    public function __construct($merchantKey)
    {
        $this->merchantKey = $merchantKey;
    }

    /**
     * 生成签名
     * 
     * @param array $params 参数数组
     * @return string 签名值
     */
    public function generateSignature(array $params)
    {
        // 第一步：过滤空值参数并排序
        $filteredParams = $this->filterAndSortParams($params);
        
        // 第二步：拼接参数字符串
        $stringA = $this->buildParamString($filteredParams);
        
        // 第三步：拼接密钥并生成签名
        $stringSignTemp = $stringA . '&key=' . $this->merchantKey;
        
        // 第四步：MD5运算并转大写
        $signature = strtoupper(md5($stringSignTemp));
        
        return $signature;
    }

    /**
     * 验证签名
     * 
     * @param array $params 参数数组（包含sign字段）
     * @return bool 验证结果
     */
    public function verifySignature(array $params)
    {
        if (!isset($params['sign'])) {
            return false;
        }
        
        $receivedSign = $params['sign'];
        
        // 移除sign参数，不参与签名计算
        unset($params['sign']);
        
        // 生成期望的签名
        $expectedSign = $this->generateSignature($params);
        
        // 比较签名
        return hash_equals($expectedSign, $receivedSign);
    }

    /**
     * 过滤空值参数并按ASCII码排序
     * 
     * @param array $params 原始参数数组
     * @return array 过滤并排序后的参数数组
     */
    private function filterAndSortParams(array $params)
    {
        $filteredParams = [];
        
        foreach ($params as $key => $value) {
            // 过滤空值参数
            if ($value !== '' && $value !== null) {
                // 处理JSON格式参数
                if (is_array($value) || is_object($value)) {
                    $value = json_encode($value, JSON_UNESCAPED_UNICODE);
                }
                $filteredParams[$key] = $value;
            }
        }
        
        // 按参数名ASCII码排序（字典序）
        ksort($filteredParams, SORT_STRING);
        
        return $filteredParams;
    }

    /**
     * 构建参数字符串
     * 
     * @param array $params 参数数组
     * @return string 参数字符串
     */
    private function buildParamString(array $params)
    {
        $paramPairs = [];
        
        foreach ($params as $key => $value) {
            $paramPairs[] = $key . '=' . $value;
        }
        
        return implode('&', $paramPairs);
    }

    /**
     * 设置商户密钥
     * 
     * @param string $merchantKey 商户密钥
     */
    public function setMerchantKey($merchantKey)
    {
        $this->merchantKey = $merchantKey;
    }

    /**
     * 获取商户密钥（仅用于调试，生产环境不建议使用）
     * 
     * @return string 商户密钥
     */
    public function getMerchantKey()
    {
        return $this->merchantKey;
    }

    /**
     * 验证参数完整性
     * 
     * @param array $params 参数数组
     * @param array $requiredFields 必需字段列表
     * @return array 验证结果 ['valid' => bool, 'missing' => array]
     */
    public function validateParams(array $params, array $requiredFields)
    {
        $missing = [];
        
        foreach ($requiredFields as $field) {
            if (!isset($params[$field]) || $params[$field] === '' || $params[$field] === null) {
                $missing[] = $field;
            }
        }
        
        return [
            'valid' => empty($missing),
            'missing' => $missing
        ];
    }

    /**
     * 格式化金额（转换为分）
     * 
     * @param float $amount 金额（美元）
     * @return int 金额（分）
     */
    public static function formatAmount($amount)
    {
        return intval(round($amount * 100));
    }

    /**
     * 反格式化金额（转换为美元）
     * 
     * @param int $amountCents 金额（分）
     * @return float 金额（美元）
     */
    public static function unformatAmount($amountCents)
    {
        return round($amountCents / 100, 2);
    }
}
