<?php
/**
 * WHMCS 企业级支付网关回调处理文件
 * 
 * 处理支付网关的异步通知回调，验证签名并更新订单状态
 * 
 * <AUTHOR> Gateway Team
 * @version 1.0.0
 */

// 引入 WHMCS 初始化文件
require_once __DIR__ . '/../../../init.php';
require_once __DIR__ . '/../../../includes/gatewayfunctions.php';
require_once __DIR__ . '/../../../includes/invoicefunctions.php';

// 引入网关核心文件
require_once __DIR__ . '/../enterprise_gateway.php';

use EnterpriseGateway\Lib\Logger;

/**
 * 主回调处理逻辑
 */
function processCallback()
{
    // 获取回调数据
    $callbackData = array_merge($_GET, $_POST);
    
    // 记录回调请求
    $logger = new Logger('INFO');
    $logger->info('Received payment callback', [
        'method' => $_SERVER['REQUEST_METHOD'],
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ]);
    
    // 验证回调数据
    if (empty($callbackData)) {
        $logger->error('Empty callback data received');
        http_response_code(400);
        echo 'FAIL: Empty callback data';
        return;
    }
    
    // 验证必需字段
    $requiredFields = ['order_id', 'status'];
    foreach ($requiredFields as $field) {
        if (!isset($callbackData[$field]) || $callbackData[$field] === '') {
            $logger->error('Missing required field in callback', ['field' => $field]);
            http_response_code(400);
            echo 'FAIL: Missing required field - ' . $field;
            return;
        }
    }
    
    $invoiceId = $callbackData['order_id'];
    
    try {
        // 获取网关配置
        $gatewayParams = getGatewayVariables('enterprise_gateway');
        
        if (!$gatewayParams['type']) {
            throw new Exception('Gateway not configured or not active');
        }
        
        // 验证发票存在
        $invoiceData = localAPI('GetInvoice', ['invoiceid' => $invoiceId]);
        
        if ($invoiceData['result'] !== 'success') {
            throw new Exception('Invoice not found: ' . $invoiceId);
        }
        
        // 处理回调
        $result = enterprise_gateway_handleCallback($gatewayParams, $callbackData);
        
        if ($result['status'] === 'completed') {
            // 支付成功，添加支付记录
            $addPaymentResult = localAPI('AddInvoicePayment', [
                'invoiceid' => $invoiceId,
                'transid' => $result['transid'],
                'amount' => $invoiceData['total'],
                'gateway' => 'enterprise_gateway',
                'date' => date('Y-m-d H:i:s'),
            ]);
            
            if ($addPaymentResult['result'] === 'success') {
                $logger->info('Payment processed successfully', [
                    'invoice_id' => $invoiceId,
                    'transaction_id' => $result['transid'],
                    'amount' => $invoiceData['total']
                ]);
                
                // 发送支付确认邮件
                sendPaymentConfirmationEmail($invoiceId, $result['transid']);
                
                echo 'SUCCESS';
            } else {
                throw new Exception('Failed to add payment: ' . $addPaymentResult['message']);
            }
            
        } elseif ($result['status'] === 'failed') {
            // 支付失败，记录日志
            $logger->warning('Payment failed', [
                'invoice_id' => $invoiceId,
                'transaction_id' => $result['transid'] ?? 'N/A',
                'callback_data' => $callbackData
            ]);
            
            echo 'FAIL';
            
        } elseif ($result['status'] === 'pending') {
            // 支付待处理
            $logger->info('Payment pending', [
                'invoice_id' => $invoiceId,
                'transaction_id' => $result['transid'] ?? 'N/A'
            ]);
            
            echo 'PENDING';
            
        } else {
            throw new Exception('Unknown payment status: ' . $result['status']);
        }
        
    } catch (Exception $e) {
        $logger->error('Callback processing error', [
            'error' => $e->getMessage(),
            'invoice_id' => $invoiceId,
            'callback_data' => $callbackData
        ]);
        
        http_response_code(500);
        echo 'ERROR: ' . $e->getMessage();
    }
}

/**
 * 发送支付确认邮件
 * 
 * @param int $invoiceId 发票ID
 * @param string $transactionId 交易ID
 */
function sendPaymentConfirmationEmail($invoiceId, $transactionId)
{
    try {
        // 发送支付确认邮件
        localAPI('SendEmail', [
            'messagename' => 'Invoice Payment Confirmation',
            'id' => $invoiceId,
            'customvars' => [
                'transaction_id' => $transactionId,
                'payment_method' => 'Enterprise Payment Gateway'
            ]
        ]);
        
    } catch (Exception $e) {
        $logger = new Logger('WARNING');
        $logger->warning('Failed to send payment confirmation email', [
            'invoice_id' => $invoiceId,
            'transaction_id' => $transactionId,
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * 验证回调来源IP（可选安全措施）
 * 
 * @param array $allowedIPs 允许的IP地址列表
 * @return bool 是否允许
 */
function validateCallbackIP($allowedIPs = [])
{
    if (empty($allowedIPs)) {
        return true; // 如果没有配置IP白名单，则允许所有IP
    }
    
    $clientIP = $_SERVER['REMOTE_ADDR'] ?? '';
    
    // 处理代理服务器的情况
    if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $forwardedIPs = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
        $clientIP = trim($forwardedIPs[0]);
    } elseif (!empty($_SERVER['HTTP_X_REAL_IP'])) {
        $clientIP = $_SERVER['HTTP_X_REAL_IP'];
    }
    
    return in_array($clientIP, $allowedIPs);
}

/**
 * 记录回调统计信息
 */
function logCallbackStats()
{
    $statsFile = __DIR__ . '/../../../logs/callback_stats.json';
    
    $stats = [];
    if (file_exists($statsFile)) {
        $stats = json_decode(file_get_contents($statsFile), true) ?: [];
    }
    
    $today = date('Y-m-d');
    if (!isset($stats[$today])) {
        $stats[$today] = ['total' => 0, 'success' => 0, 'failed' => 0];
    }
    
    $stats[$today]['total']++;
    
    // 保留最近30天的统计
    $cutoffDate = date('Y-m-d', strtotime('-30 days'));
    foreach ($stats as $date => $data) {
        if ($date < $cutoffDate) {
            unset($stats[$date]);
        }
    }
    
    file_put_contents($statsFile, json_encode($stats, JSON_PRETTY_PRINT));
}

// 设置响应头
header('Content-Type: text/plain');
header('Cache-Control: no-cache, no-store, must-revalidate');

// 记录统计信息
logCallbackStats();

// 处理回调
processCallback();
?>
