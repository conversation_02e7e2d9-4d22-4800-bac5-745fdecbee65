<?php
/**
 * 企业级支付网关日志记录器
 * 
 * 提供完整的日志记录功能，支持不同级别的日志输出
 * 
 * <AUTHOR> Gateway Team
 * @version 1.0.0
 */

namespace EnterpriseGateway\Lib;

class Logger
{
    /**
     * 日志级别常量
     */
    const LEVEL_DEBUG = 0;
    const LEVEL_INFO = 1;
    const LEVEL_WARNING = 2;
    const LEVEL_ERROR = 3;
    const LEVEL_CRITICAL = 4;

    /**
     * 日志级别映射
     * @var array
     */
    private static $levelMap = [
        'DEBUG' => self::LEVEL_DEBUG,
        'INFO' => self::LEVEL_INFO,
        'WARNING' => self::LEVEL_WARNING,
        'ERROR' => self::LEVEL_ERROR,
        'CRITICAL' => self::LEVEL_CRITICAL
    ];

    /**
     * 当前日志级别
     * @var int
     */
    private $logLevel;

    /**
     * 日志文件路径
     * @var string
     */
    private $logFile;

    /**
     * 构造函数
     * 
     * @param string $level 日志级别
     * @param string $logFile 日志文件路径
     */
    public function __construct($level = 'INFO', $logFile = null)
    {
        $this->logLevel = self::$levelMap[strtoupper($level)] ?? self::LEVEL_INFO;
        $this->logFile = $logFile ?: $this->getDefaultLogFile();
        
        // 确保日志目录存在
        $this->ensureLogDirectory();
    }

    /**
     * 记录调试信息
     * 
     * @param string $message 日志消息
     * @param array $context 上下文数据
     */
    public function debug($message, array $context = [])
    {
        $this->log('DEBUG', $message, $context);
    }

    /**
     * 记录一般信息
     * 
     * @param string $message 日志消息
     * @param array $context 上下文数据
     */
    public function info($message, array $context = [])
    {
        $this->log('INFO', $message, $context);
    }

    /**
     * 记录警告信息
     * 
     * @param string $message 日志消息
     * @param array $context 上下文数据
     */
    public function warning($message, array $context = [])
    {
        $this->log('WARNING', $message, $context);
    }

    /**
     * 记录错误信息
     * 
     * @param string $message 日志消息
     * @param array $context 上下文数据
     */
    public function error($message, array $context = [])
    {
        $this->log('ERROR', $message, $context);
    }

    /**
     * 记录严重错误信息
     * 
     * @param string $message 日志消息
     * @param array $context 上下文数据
     */
    public function critical($message, array $context = [])
    {
        $this->log('CRITICAL', $message, $context);
    }

    /**
     * 记录日志
     * 
     * @param string $level 日志级别
     * @param string $message 日志消息
     * @param array $context 上下文数据
     */
    private function log($level, $message, array $context = [])
    {
        $levelValue = self::$levelMap[$level] ?? self::LEVEL_INFO;
        
        // 检查日志级别
        if ($levelValue < $this->logLevel) {
            return;
        }

        // 构建日志条目
        $logEntry = $this->buildLogEntry($level, $message, $context);
        
        // 写入日志文件
        $this->writeToFile($logEntry);
        
        // 如果是错误级别，同时输出到错误日志
        if ($levelValue >= self::LEVEL_ERROR) {
            error_log($logEntry);
        }
    }

    /**
     * 构建日志条目
     * 
     * @param string $level 日志级别
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return string 格式化的日志条目
     */
    private function buildLogEntry($level, $message, array $context)
    {
        $timestamp = date('Y-m-d H:i:s');
        $contextStr = empty($context) ? '' : ' | Context: ' . json_encode($context, JSON_UNESCAPED_UNICODE);
        
        return sprintf(
            "[%s] [%s] %s%s%s",
            $timestamp,
            $level,
            $message,
            $contextStr,
            PHP_EOL
        );
    }

    /**
     * 写入日志文件
     * 
     * @param string $logEntry 日志条目
     */
    private function writeToFile($logEntry)
    {
        try {
            file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
        } catch (\Exception $e) {
            // 如果无法写入日志文件，输出到错误日志
            error_log("Failed to write to log file: " . $e->getMessage());
            error_log($logEntry);
        }
    }

    /**
     * 获取默认日志文件路径
     * 
     * @return string 日志文件路径
     */
    private function getDefaultLogFile()
    {
        $logDir = dirname(__DIR__, 4) . '/logs';
        return $logDir . '/enterprise_gateway_' . date('Y-m-d') . '.log';
    }

    /**
     * 确保日志目录存在
     */
    private function ensureLogDirectory()
    {
        $logDir = dirname($this->logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }

    /**
     * 设置日志级别
     * 
     * @param string $level 日志级别
     */
    public function setLogLevel($level)
    {
        $this->logLevel = self::$levelMap[strtoupper($level)] ?? self::LEVEL_INFO;
    }

    /**
     * 获取当前日志级别
     * 
     * @return string 日志级别
     */
    public function getLogLevel()
    {
        return array_search($this->logLevel, self::$levelMap);
    }

    /**
     * 清理旧日志文件
     * 
     * @param int $days 保留天数
     */
    public function cleanOldLogs($days = 30)
    {
        $logDir = dirname($this->logFile);
        $cutoffTime = time() - ($days * 24 * 60 * 60);
        
        if (is_dir($logDir)) {
            $files = glob($logDir . '/enterprise_gateway_*.log');
            
            foreach ($files as $file) {
                if (filemtime($file) < $cutoffTime) {
                    unlink($file);
                    $this->info('Cleaned old log file', ['file' => basename($file)]);
                }
            }
        }
    }

    /**
     * 记录支付相关的敏感信息（自动脱敏）
     * 
     * @param string $level 日志级别
     * @param string $message 日志消息
     * @param array $context 上下文数据
     */
    public function logPaymentData($level, $message, array $context = [])
    {
        // 脱敏处理
        $sanitizedContext = $this->sanitizePaymentData($context);
        $this->log($level, $message, $sanitizedContext);
    }

    /**
     * 脱敏支付数据
     * 
     * @param array $data 原始数据
     * @return array 脱敏后的数据
     */
    private function sanitizePaymentData(array $data)
    {
        $sensitiveFields = ['merchant_key', 'sign', 'password', 'secret'];
        
        foreach ($data as $key => $value) {
            if (in_array(strtolower($key), $sensitiveFields)) {
                $data[$key] = '***MASKED***';
            } elseif (is_array($value)) {
                $data[$key] = $this->sanitizePaymentData($value);
            }
        }
        
        return $data;
    }
}
