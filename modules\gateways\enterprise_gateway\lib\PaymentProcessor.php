<?php
/**
 * 企业级支付网关支付处理器
 * 
 * 处理支付请求、回调验证、状态更新等核心业务逻辑
 * 
 * <AUTHOR> Gateway Team
 * @version 1.0.0
 */

namespace EnterpriseGateway\Lib;

require_once __DIR__ . '/SignatureHelper.php';
require_once __DIR__ . '/Logger.php';

class PaymentProcessor
{
    /**
     * 签名助手实例
     * @var SignatureHelper
     */
    private $signatureHelper;

    /**
     * 日志记录器
     * @var Logger
     */
    private $logger;

    /**
     * 支付网关配置
     * @var array
     */
    private $config;

    /**
     * 构造函数
     * 
     * @param array $config 网关配置
     */
    public function __construct(array $config)
    {
        $this->config = $config;
        $this->signatureHelper = new SignatureHelper($config['merchant_key']);
        $this->logger = new Logger($config['log_level'] ?? 'INFO');
    }

    /**
     * 创建支付请求
     * 
     * @param array $orderData 订单数据
     * @return array 支付请求结果
     */
    public function createPaymentRequest(array $orderData)
    {
        try {
            $this->logger->info('Creating payment request', $orderData);

            // 验证必需参数
            $requiredFields = ['order_id', 'amount', 'currency', 'return_url', 'notify_url'];
            $validation = $this->signatureHelper->validateParams($orderData, $requiredFields);
            
            if (!$validation['valid']) {
                throw new \InvalidArgumentException('Missing required fields: ' . implode(', ', $validation['missing']));
            }

            // 构建支付参数
            $paymentParams = $this->buildPaymentParams($orderData);
            
            // 生成签名
            $paymentParams['sign'] = $this->signatureHelper->generateSignature($paymentParams);
            
            $this->logger->info('Payment request created successfully', ['order_id' => $orderData['order_id']]);
            
            return [
                'success' => true,
                'payment_url' => $this->config['payment_url'],
                'params' => $paymentParams,
                'method' => 'POST'
            ];

        } catch (\Exception $e) {
            $this->logger->error('Failed to create payment request', [
                'error' => $e->getMessage(),
                'order_data' => $orderData
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 处理支付回调
     * 
     * @param array $callbackData 回调数据
     * @return array 处理结果
     */
    public function handleCallback(array $callbackData)
    {
        try {
            $this->logger->info('Processing payment callback', $callbackData);

            // 验证签名
            if (!$this->signatureHelper->verifySignature($callbackData)) {
                throw new \SecurityException('Invalid signature');
            }

            // 验证必需字段
            $requiredFields = ['order_id', 'transaction_id', 'amount', 'status'];
            $validation = $this->signatureHelper->validateParams($callbackData, $requiredFields);
            
            if (!$validation['valid']) {
                throw new \InvalidArgumentException('Missing required callback fields: ' . implode(', ', $validation['missing']));
            }

            // 处理支付状态
            $result = $this->processPaymentStatus($callbackData);
            
            $this->logger->info('Payment callback processed successfully', [
                'order_id' => $callbackData['order_id'],
                'status' => $callbackData['status']
            ]);
            
            return $result;

        } catch (\Exception $e) {
            $this->logger->error('Failed to process payment callback', [
                'error' => $e->getMessage(),
                'callback_data' => $callbackData
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 构建支付参数
     * 
     * @param array $orderData 订单数据
     * @return array 支付参数
     */
    private function buildPaymentParams(array $orderData)
    {
        // 格式化金额为分
        $amountCents = SignatureHelper::formatAmount($orderData['amount']);
        
        return [
            'merchant_id' => $this->config['merchant_id'],
            'order_id' => $orderData['order_id'],
            'amount' => $amountCents,
            'currency' => $orderData['currency'] ?? 'USD',
            'subject' => $orderData['subject'] ?? 'Payment for Order #' . $orderData['order_id'],
            'return_url' => $orderData['return_url'],
            'notify_url' => $orderData['notify_url'],
            'timestamp' => time(),
            'version' => '1.0'
        ];
    }

    /**
     * 处理支付状态
     * 
     * @param array $callbackData 回调数据
     * @return array 处理结果
     */
    private function processPaymentStatus(array $callbackData)
    {
        $status = strtoupper($callbackData['status']);
        
        switch ($status) {
            case 'SUCCESS':
            case 'PAID':
                return [
                    'success' => true,
                    'status' => 'completed',
                    'transaction_id' => $callbackData['transaction_id'],
                    'amount' => SignatureHelper::unformatAmount($callbackData['amount']),
                    'message' => 'Payment completed successfully'
                ];
                
            case 'FAILED':
            case 'ERROR':
                return [
                    'success' => true,
                    'status' => 'failed',
                    'transaction_id' => $callbackData['transaction_id'] ?? null,
                    'message' => $callbackData['message'] ?? 'Payment failed'
                ];
                
            case 'PENDING':
            case 'PROCESSING':
                return [
                    'success' => true,
                    'status' => 'pending',
                    'transaction_id' => $callbackData['transaction_id'] ?? null,
                    'message' => 'Payment is being processed'
                ];
                
            default:
                throw new \InvalidArgumentException('Unknown payment status: ' . $status);
        }
    }

    /**
     * 查询支付状态
     * 
     * @param string $orderId 订单ID
     * @return array 查询结果
     */
    public function queryPaymentStatus($orderId)
    {
        try {
            $this->logger->info('Querying payment status', ['order_id' => $orderId]);

            $queryParams = [
                'merchant_id' => $this->config['merchant_id'],
                'order_id' => $orderId,
                'timestamp' => time(),
                'version' => '1.0'
            ];

            // 生成签名
            $queryParams['sign'] = $this->signatureHelper->generateSignature($queryParams);

            // 发送查询请求
            $response = $this->sendHttpRequest($this->config['query_url'], $queryParams);
            
            if ($response['success'] && $this->signatureHelper->verifySignature($response['data'])) {
                return [
                    'success' => true,
                    'status' => $response['data']['status'],
                    'transaction_id' => $response['data']['transaction_id'] ?? null,
                    'amount' => isset($response['data']['amount']) ? SignatureHelper::unformatAmount($response['data']['amount']) : null
                ];
            } else {
                throw new \RuntimeException('Invalid query response or signature verification failed');
            }

        } catch (\Exception $e) {
            $this->logger->error('Failed to query payment status', [
                'error' => $e->getMessage(),
                'order_id' => $orderId
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 发送HTTP请求
     * 
     * @param string $url 请求URL
     * @param array $params 请求参数
     * @return array 响应结果
     */
    private function sendHttpRequest($url, array $params)
    {
        $postData = http_build_query($params);
        
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => 'Content-Type: application/x-www-form-urlencoded',
                'content' => $postData,
                'timeout' => 30
            ]
        ]);

        $response = file_get_contents($url, false, $context);
        
        if ($response === false) {
            throw new \RuntimeException('HTTP request failed');
        }

        $data = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \RuntimeException('Invalid JSON response');
        }

        return [
            'success' => true,
            'data' => $data
        ];
    }
}
