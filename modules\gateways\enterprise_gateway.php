<?php
/**
 * WHMCS 8.X 企业级支付网关模块
 * 
 * 符合 WHMCS 8.X 标准的支付网关实现，支持：
 * - MD5 签名算法
 * - HTTPS 安全传输
 * - 完整的错误处理和日志记录
 * - 灵活的配置管理
 * 
 * <AUTHOR> Gateway Team
 * @version 1.0.0
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

// 引入核心类库
require_once __DIR__ . '/enterprise_gateway/lib/SignatureHelper.php';
require_once __DIR__ . '/enterprise_gateway/lib/PaymentProcessor.php';
require_once __DIR__ . '/enterprise_gateway/lib/Logger.php';

use EnterpriseGateway\Lib\SignatureHelper;
use EnterpriseGateway\Lib\PaymentProcessor;
use EnterpriseGateway\Lib\Logger;

/**
 * 定义网关元数据
 * 
 * @return array 网关配置信息
 */
function enterprise_gateway_MetaData()
{
    return [
        'DisplayName' => 'Enterprise Payment Gateway',
        'APIVersion' => '1.1',
        'DisableLocalCreditCardInput' => true,
        'TokenisedStorage' => false,
    ];
}

/**
 * 定义网关配置字段
 * 
 * @return array 配置字段定义
 */
function enterprise_gateway_config()
{
    return [
        'FriendlyName' => [
            'Type' => 'System',
            'Value' => 'Enterprise Payment Gateway',
        ],
        'merchant_id' => [
            'FriendlyName' => 'Merchant ID',
            'Type' => 'text',
            'Size' => '20',
            'Default' => '',
            'Description' => 'Enter your merchant ID provided by the payment gateway',
        ],
        'merchant_key' => [
            'FriendlyName' => 'Merchant Key',
            'Type' => 'password',
            'Size' => '50',
            'Default' => '',
            'Description' => 'Enter your merchant secret key for signature generation',
        ],
        'payment_url' => [
            'FriendlyName' => 'Payment URL',
            'Type' => 'text',
            'Size' => '100',
            'Default' => 'https://api.payment-gateway.com/pay',
            'Description' => 'Payment gateway API endpoint URL',
        ],
        'query_url' => [
            'FriendlyName' => 'Query URL',
            'Type' => 'text',
            'Size' => '100',
            'Default' => 'https://api.payment-gateway.com/query',
            'Description' => 'Payment status query API endpoint URL',
        ],
        'test_mode' => [
            'FriendlyName' => 'Test Mode',
            'Type' => 'yesno',
            'Description' => 'Enable test mode for development and testing',
        ],
        'log_level' => [
            'FriendlyName' => 'Log Level',
            'Type' => 'dropdown',
            'Options' => [
                'DEBUG' => 'Debug',
                'INFO' => 'Info',
                'WARNING' => 'Warning',
                'ERROR' => 'Error',
                'CRITICAL' => 'Critical',
            ],
            'Default' => 'INFO',
            'Description' => 'Set the logging level for the payment gateway',
        ],
        'currency_support' => [
            'FriendlyName' => 'Supported Currencies',
            'Type' => 'text',
            'Size' => '50',
            'Default' => 'USD',
            'Description' => 'Comma-separated list of supported currencies (e.g., USD,EUR,GBP)',
        ],
    ];
}

/**
 * 生成支付链接
 * 
 * @param array $params WHMCS 传递的参数
 * @return string 支付表单HTML
 */
function enterprise_gateway_link($params)
{
    try {
        // 初始化支付处理器
        $processor = new PaymentProcessor($params);
        
        // 构建订单数据
        $orderData = [
            'order_id' => $params['invoiceid'],
            'amount' => $params['amount'],
            'currency' => $params['currency'],
            'subject' => 'Invoice #' . $params['invoiceid'] . ' - ' . $params['companyname'],
            'return_url' => $params['returnurl'],
            'notify_url' => $params['systemurl'] . 'modules/gateways/callback/enterprise_gateway.php',
            'customer_email' => $params['clientdetails']['email'],
            'customer_name' => $params['clientdetails']['firstname'] . ' ' . $params['clientdetails']['lastname'],
        ];
        
        // 创建支付请求
        $paymentRequest = $processor->createPaymentRequest($orderData);
        
        if (!$paymentRequest['success']) {
            throw new Exception('Failed to create payment request: ' . $paymentRequest['error']);
        }
        
        // 生成支付表单
        return generatePaymentForm($paymentRequest, $params);
        
    } catch (Exception $e) {
        $logger = new Logger($params['log_level'] ?? 'ERROR');
        $logger->error('Payment link generation failed', [
            'error' => $e->getMessage(),
            'invoice_id' => $params['invoiceid']
        ]);
        
        return '<div class="alert alert-danger">Payment gateway error: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * 生成支付表单HTML
 * 
 * @param array $paymentRequest 支付请求数据
 * @param array $params WHMCS参数
 * @return string 支付表单HTML
 */
function generatePaymentForm($paymentRequest, $params)
{
    $formFields = '';
    foreach ($paymentRequest['params'] as $key => $value) {
        $formFields .= '<input type="hidden" name="' . htmlspecialchars($key) . '" value="' . htmlspecialchars($value) . '">' . "\n";
    }
    
    $testModeNotice = $params['test_mode'] ? '<div class="alert alert-info">Test Mode Enabled</div>' : '';
    
    return '
    <div class="enterprise-gateway-payment">
        ' . $testModeNotice . '
        <form method="post" action="' . htmlspecialchars($paymentRequest['payment_url']) . '" id="enterprise-payment-form">
            ' . $formFields . '
            <div class="payment-info">
                <h4>Payment Details</h4>
                <p><strong>Amount:</strong> ' . htmlspecialchars($params['amount']) . ' ' . htmlspecialchars($params['currency']) . '</p>
                <p><strong>Invoice:</strong> #' . htmlspecialchars($params['invoiceid']) . '</p>
            </div>
            <div class="payment-actions">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fa fa-credit-card"></i> Pay Now
                </button>
                <button type="button" class="btn btn-secondary" onclick="history.back()">
                    <i class="fa fa-arrow-left"></i> Cancel
                </button>
            </div>
        </form>
        
        <script>
        document.addEventListener("DOMContentLoaded", function() {
            // 自动提交表单（可选）
            // document.getElementById("enterprise-payment-form").submit();
        });
        </script>
        
        <style>
        .enterprise-gateway-payment {
            max-width: 500px;
            margin: 20px auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .payment-info {
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 3px;
        }
        .payment-actions {
            text-align: center;
        }
        .payment-actions .btn {
            margin: 0 10px;
            padding: 10px 20px;
        }
        </style>
    </div>';
}

/**
 * 处理支付回调（在 callback 文件中调用）
 * 
 * @param array $params 网关配置参数
 * @param array $callbackData 回调数据
 * @return array 处理结果
 */
function enterprise_gateway_handleCallback($params, $callbackData)
{
    try {
        $processor = new PaymentProcessor($params);
        $result = $processor->handleCallback($callbackData);
        
        if ($result['success']) {
            return [
                'status' => $result['status'],
                'transid' => $result['transaction_id'] ?? '',
                'rawdata' => $callbackData,
            ];
        } else {
            throw new Exception($result['error']);
        }
        
    } catch (Exception $e) {
        $logger = new Logger($params['log_level'] ?? 'ERROR');
        $logger->error('Callback processing failed', [
            'error' => $e->getMessage(),
            'callback_data' => $callbackData
        ]);
        
        return [
            'status' => 'error',
            'rawdata' => $callbackData,
        ];
    }
}

/**
 * 查询支付状态（管理员功能）
 * 
 * @param array $params 网关配置参数
 * @param string $invoiceId 发票ID
 * @return array 查询结果
 */
function enterprise_gateway_queryStatus($params, $invoiceId)
{
    try {
        $processor = new PaymentProcessor($params);
        return $processor->queryPaymentStatus($invoiceId);
        
    } catch (Exception $e) {
        $logger = new Logger($params['log_level'] ?? 'ERROR');
        $logger->error('Status query failed', [
            'error' => $e->getMessage(),
            'invoice_id' => $invoiceId
        ]);
        
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * 网关健康检查
 * 
 * @param array $params 网关配置参数
 * @return array 检查结果
 */
function enterprise_gateway_healthCheck($params)
{
    $checks = [];
    
    // 检查必需配置
    $requiredConfigs = ['merchant_id', 'merchant_key', 'payment_url'];
    foreach ($requiredConfigs as $config) {
        $checks[$config] = !empty($params[$config]);
    }
    
    // 检查URL可访问性
    $checks['payment_url_accessible'] = checkUrlAccessibility($params['payment_url']);
    
    // 检查签名算法
    try {
        $signatureHelper = new SignatureHelper($params['merchant_key']);
        $testParams = ['test' => 'value', 'amount' => 100];
        $signature = $signatureHelper->generateSignature($testParams);
        $checks['signature_generation'] = !empty($signature);
    } catch (Exception $e) {
        $checks['signature_generation'] = false;
    }
    
    return $checks;
}

/**
 * 检查URL可访问性
 * 
 * @param string $url URL地址
 * @return bool 是否可访问
 */
function checkUrlAccessibility($url)
{
    $context = stream_context_create([
        'http' => [
            'method' => 'HEAD',
            'timeout' => 10
        ]
    ]);
    
    $headers = @get_headers($url, 1, $context);
    return $headers !== false;
}
